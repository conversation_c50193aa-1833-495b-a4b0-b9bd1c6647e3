import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'LAskill Training Center - Discover, Learn, Monetize Digital Skills',
  description: 'Gain in-demand digital skills with tailored training, resources, and community support, all designed to boost your confidence and fast-track your success.',
  keywords: 'digital skills, training, freelancing, web development, digital marketing, Nigeria',
  authors: [{ name: '<PERSON>' }],
  openGraph: {
    title: 'LAskill Training Center',
    description: 'Discover, Learn, and Monetize Digital Skills for the Future!',
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'LAskill Training Center',
    description: 'Discover, Learn, and Monetize Digital Skills for the Future!',
  },
  viewport: 'width=device-width, initial-scale=1',
  robots: 'index, follow',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  )
}
