'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { BookO<PERSON>, Clock, ArrowRight, User, ExternalLink } from 'lucide-react'

const Resources = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const articles = [
    {
      title: 'How Much Experience Do I Need Before Becoming a Freelancer?',
      excerpt: 'Discover the minimum experience requirements and how to get started in freelancing even as a beginner.',
      author: '<PERSON>',
      readTime: '5 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/Becoming-A-Freelancer-LAskill-Training-Center-1024x683.jpg',
      category: 'Freelancing',
      featured: true
    },
    {
      title: 'Most In-Demand Freelance Services (And How to Get Started!)',
      excerpt: 'Learn about the most profitable freelance services in 2024 and how to position yourself in these markets.',
      author: '<PERSON>',
      readTime: '7 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/In-Demand-Freelance-Service-LAskill-Training-Center-1024x683.jpg',
      category: 'Career',
      featured: false
    },
    {
      title: 'Freelance Job Interview Questions and How to Answer Them',
      excerpt: 'Master the art of freelance interviews with these proven strategies and sample answers.',
      author: 'Charles Izuoba',
      readTime: '6 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/Freelance-Job-Interview-LAskill-Training-Center-1024x683.jpg',
      category: 'Interview',
      featured: false
    },
    {
      title: '6 Things Clients Need from You Before Giving You a Freelance Job',
      excerpt: 'Understand what clients really want and how to position yourself as the perfect candidate.',
      author: 'Charles Izuoba',
      readTime: '4 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/6-Things-Clients-Need-from-You-LAskill-Training-Center-1024x683.jpg',
      category: 'Client Relations',
      featured: false
    },
    {
      title: '7 Easiest Freelance Jobs for Beginner +How To Start',
      excerpt: 'Start your freelancing journey with these beginner-friendly opportunities that require minimal experience.',
      author: 'Charles Izuoba',
      readTime: '8 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/7-easiest-freelance-jobs-LAskill-Training-Center-1024x683.jpg',
      category: 'Beginner Guide',
      featured: false
    },
    {
      title: 'How to Find and Secure Freelance Gigs Without Connection',
      excerpt: 'Proven strategies to land freelance jobs even if you don\'t have industry connections.',
      author: 'Charles Izuoba',
      readTime: '6 min read',
      image: 'https://laskill.org/wp-content/uploads/2025/03/Securing-Freelance-Gigs-in-Nigeria-LAskill-Training-Center-1024x683.jpg',
      category: 'Job Search',
      featured: false
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="resources" ref={ref} className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
            Insights & Career Paths for{' '}
            <span className="text-gradient">Freelancers</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover Insightful Resources for Your Digital Growth
          </p>
        </motion.div>

        {/* Featured Article */}
        {articles.filter(article => article.featured).map((article, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-16"
          >
            <div className="card overflow-hidden">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <img
                    src={article.image}
                    alt={article.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-primary-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Featured
                    </span>
                  </div>
                </div>
                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-sm font-medium">
                      {article.category}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {article.readTime}
                    </div>
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 leading-tight">
                    {article.title}
                  </h3>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {article.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <User className="w-5 h-5 text-gray-400" />
                      <span className="text-gray-700 font-medium">{article.author}</span>
                    </div>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="btn-primary flex items-center space-x-2"
                    >
                      <span>Read More</span>
                      <ArrowRight className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}

        {/* Articles Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {articles.filter(article => !article.featured).map((article, index) => (
            <motion.article
              key={index}
              variants={cardVariants}
              whileHover={{ 
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              className="card overflow-hidden group cursor-pointer"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={article.image}
                  alt={article.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="absolute top-4 left-4">
                  <span className="bg-white/90 text-gray-800 px-3 py-1 rounded-full text-sm font-medium">
                    {article.category}
                  </span>
                </div>
              </div>
              
              <div className="p-6">
                <div className="flex items-center text-gray-500 text-sm mb-3">
                  <Clock className="w-4 h-4 mr-1" />
                  {article.readTime}
                </div>
                
                <h3 className="text-lg font-bold text-gray-900 mb-3 leading-tight group-hover:text-primary-600 transition-colors duration-200">
                  {article.title}
                </h3>
                
                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {article.excerpt}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <User className="w-4 h-4 text-gray-400" />
                    <span className="text-gray-700 text-sm font-medium">{article.author}</span>
                  </div>
                  <ExternalLink className="w-4 h-4 text-gray-400 group-hover:text-primary-600 transition-colors duration-200" />
                </div>
              </div>
            </motion.article>
          ))}
        </motion.div>

        {/* Load More Button */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ delay: 1 }}
          className="text-center mt-12"
        >
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="bg-white hover:bg-gray-50 text-gray-800 font-semibold py-3 px-8 rounded-lg border-2 border-gray-200 hover:border-primary-300 transition-all duration-300 flex items-center space-x-2 mx-auto"
          >
            <BookOpen className="w-5 h-5" />
            <span>Load More Articles</span>
          </motion.button>
        </motion.div>
      </div>
    </section>
  )
}

export default Resources
