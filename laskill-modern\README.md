# LAskill Modern Website

A modern, responsive rebuild of the LAskill Training Center website with enhanced automation and mobile-friendly design.

## Features

- **Modern Design**: Clean, professional interface with smooth animations
- **Mobile-First**: Fully responsive design optimized for all devices
- **Automated Elements**: Smooth scrolling, hover effects, and interactive components
- **Performance Optimized**: Built with Next.js 14 for fast loading times
- **Accessibility**: WCAG compliant with proper semantic HTML

## Technology Stack

- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS for responsive design
- **Animations**: Framer Motion for smooth interactions
- **Icons**: Lucide React for modern iconography
- **Deployment**: Optimized for Vercel deployment

## Getting Started

1. **Install Dependencies**:
   ```bash
   npm install
   ```

2. **Run Development Server**:
   ```bash
   npm run dev
   ```

3. **Open Browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Project Structure

```
src/
├── app/
│   ├── layout.tsx      # Root layout
│   ├── page.tsx        # Homepage
│   └── globals.css     # Global styles
└── components/
    ├── Header.tsx      # Navigation header
    ├── Hero.tsx        # Hero section
    ├── About.tsx       # About section
    ├── ThreePhaseModel.tsx # Discover/Learn/Monetize
    ├── Resources.tsx   # Blog/Resources
    ├── Events.tsx      # Upcoming events
    └── Footer.tsx      # Footer with links
```

## Key Improvements

1. **Enhanced User Experience**:
   - Smooth scrolling navigation
   - Interactive hover effects
   - Loading animations
   - Mobile-optimized touch interactions

2. **Modern Automation**:
   - Auto-playing animations
   - Scroll-triggered effects
   - Dynamic content loading
   - Responsive image optimization

3. **Performance**:
   - Code splitting
   - Image optimization
   - CSS optimization
   - Fast page loads

## Contact Information

- **Phone**: 08121073763 / 08165183620
- **Email**: <EMAIL>
- **Website**: [laskill.org](https://laskill.org)

## License

© 2025 LAskill Training Center. All rights reserved.
