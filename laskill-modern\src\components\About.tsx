'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { Quote, Mail, Users, Target, Award } from 'lucide-react'

const About = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const achievements = [
    { icon: Users, number: '2,500+', text: 'Students Trained' },
    { icon: Target, number: '10+', text: 'Years Experience' },
    { icon: Award, number: '100%', text: 'Practical Training' },
  ]

  return (
    <section id="about" ref={ref} className="py-20 lg:py-32 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20 items-center">
          {/* Image Section */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8 }}
            className="relative"
          >
            <div className="relative">
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="relative z-10 rounded-2xl overflow-hidden shadow-2xl"
              >
                <img
                  src="https://laskill.org/wp-content/uploads/2024/11/Charles-Ifeanyi-Izuoba-LAskill-CEO-1024x1024.jpg"
                  alt="Charles Ifeanyi Izuoba - LAskill CEO"
                  className="w-full h-auto object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </motion.div>
              
              {/* Floating elements */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute -top-6 -right-6 w-24 h-24 bg-primary-500 rounded-full opacity-20"
              />
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="absolute -bottom-6 -left-6 w-32 h-32 bg-accent-500 rounded-full opacity-20"
              />
            </div>

            {/* Achievement Cards */}
            <div className="grid grid-cols-3 gap-4 mt-8">
              {achievements.map((achievement, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className="card p-4 text-center"
                >
                  <achievement.icon className="w-6 h-6 text-primary-600 mx-auto mb-2" />
                  <div className="text-lg font-bold text-gray-900">{achievement.number}</div>
                  <div className="text-xs text-gray-600">{achievement.text}</div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={isInView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="space-y-8"
          >
            <div>
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: 0.4 }}
                className="text-3xl lg:text-5xl font-bold text-gray-900 mb-6"
              >
                The Vision Behind{' '}
                <span className="text-gradient">LAskill</span>
              </motion.h2>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ delay: 0.6 }}
                className="relative"
              >
                <Quote className="w-8 h-8 text-primary-500 mb-4" />
                <p className="text-lg text-gray-700 leading-relaxed mb-6">
                  Charles Ifeanyi Izuoba's journey into the digital world began with a simple realization — 
                  opportunities were shifting, and the future belonged to those who could harness the power of digital skills.
                </p>
              </motion.div>
            </div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 0.8 }}
              className="space-y-4"
            >
              <p className="text-gray-700 leading-relaxed">
                With over a decade of experience in web development, digital marketing, and public speaking, 
                Charles understood firsthand the challenges individuals and businesses faced when trying to 
                navigate this fast-evolving landscape.
              </p>
              
              <p className="text-gray-700 leading-relaxed">
                Driven by a passion for empowerment, Charles founded <strong>LAskill</strong>—a platform 
                dedicated to providing accessible and affordable digital skills training for individuals 
                and organizations.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 1 }}
              className="bg-primary-50 rounded-xl p-6 border-l-4 border-primary-500"
            >
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Our Mission</h3>
              <p className="text-gray-700 leading-relaxed">
                To help people not just learn, but thrive. With over 2,500 students trained, both online 
                and offline, LAskill became a beacon for those looking to succeed in the digital economy.
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ delay: 1.2 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <motion.a
                href="mailto:<EMAIL>"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="btn-primary flex items-center justify-center space-x-2"
              >
                <Mail className="w-5 h-5" />
                <span>Connect with Charles</span>
              </motion.a>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-3 px-6 rounded-lg transition-all duration-300"
              >
                Read Full Story
              </motion.button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
