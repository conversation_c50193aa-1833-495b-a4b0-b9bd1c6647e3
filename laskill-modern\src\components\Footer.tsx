'use client'

import { motion } from 'framer-motion'
import {
  Facebook,
  Youtube,
  Instagram,
  Linkedin,
  Phone,
  Mail,
  MapPin,
  ArrowUp,
  Star,
  MessageCircle
} from 'lucide-react'

// TikTok icon component since it's not in lucide-react
const TikTokIcon = ({ className }: { className?: string }) => (
  <svg className={className} viewBox="0 0 24 24" fill="currentColor">
    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.1z"/>
  </svg>
)

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const socialLinks = [
    { icon: Facebook, href: '#facebook', label: 'Facebook' },
    { icon: Youtube, href: '#youtube', label: 'YouTube' },
    { icon: TikTokIcon, href: '#tiktok', label: 'TikTok' },
    { icon: Instagram, href: '#instagram', label: 'Instagram' },
    { icon: Linkedin, href: '#linkedin', label: 'LinkedIn' },
  ]

  const quickLinks = [
    { name: 'About Us', href: '#about' },
    { name: 'Our Courses', href: '#courses' },
    { name: 'Free Resources', href: '#resources' },
    { name: 'Contact Us', href: '#contact' },
    { name: 'Student Login', href: '#login' },
  ]

  const resources = [
    { name: 'Freelancing Guide', href: '#freelancing' },
    { name: 'Interactive Quiz', href: '#quiz' },
    { name: 'Free Webinar', href: '#webinar' },
    { name: 'Terms of Use', href: '#terms' },
  ]

  return (
    <footer className="bg-gray-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      <div className="relative z-10">
        {/* Main Footer Content */}
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
            {/* Company Info */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="lg:col-span-2"
            >
              <motion.h3
                whileHover={{ scale: 1.05 }}
                className="text-3xl font-bold text-gradient mb-4"
              >
                LAskill
              </motion.h3>
              <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
                LAskill is a training institute focused on helping individuals to discover, learn, 
                and monetize their skills by providing private and group training for interested 
                persons or organizations.
              </p>
              
              {/* Contact Info */}
              <div className="space-y-3 mb-6">
                <motion.a
                  href="tel:08121073763"
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="flex items-center space-x-3 text-gray-300 hover:text-accent-500 transition-colors duration-200"
                >
                  <Phone className="w-5 h-5" />
                  <span>08121073763 / 08165183620</span>
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="flex items-center space-x-3 text-gray-300 hover:text-accent-500 transition-colors duration-200"
                >
                  <Mail className="w-5 h-5" />
                  <span><EMAIL></span>
                </motion.a>
                <motion.div
                  whileHover={{ scale: 1.02, x: 5 }}
                  className="flex items-center space-x-3 text-gray-300"
                >
                  <MapPin className="w-5 h-5" />
                  <span>Nigeria</span>
                </motion.div>
              </div>

              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => (
                  <motion.a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    whileHover={{ scale: 1.1, y: -2 }}
                    whileTap={{ scale: 0.95 }}
                    className="w-10 h-10 bg-gray-800 hover:bg-primary-600 rounded-full flex items-center justify-center transition-all duration-300"
                    aria-label={social.label}
                  >
                    <social.icon className="w-5 h-5" />
                  </motion.a>
                ))}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <motion.li key={index}>
                    <motion.a
                      href={link.href}
                      whileHover={{ x: 5 }}
                      className="text-gray-300 hover:text-accent-500 transition-all duration-200 flex items-center space-x-2"
                    >
                      <span className="w-1 h-1 bg-accent-500 rounded-full" />
                      <span>{link.name}</span>
                    </motion.a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>

            {/* Resources */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <h4 className="text-lg font-semibold mb-6">Resources</h4>
              <ul className="space-y-3">
                {resources.map((resource, index) => (
                  <motion.li key={index}>
                    <motion.a
                      href={resource.href}
                      whileHover={{ x: 5 }}
                      className="text-gray-300 hover:text-accent-500 transition-all duration-200 flex items-center space-x-2"
                    >
                      <span className="w-1 h-1 bg-accent-500 rounded-full" />
                      <span>{resource.name}</span>
                    </motion.a>
                  </motion.li>
                ))}
              </ul>
            </motion.div>
          </div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="border-t border-gray-800"
        >
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
              <div>
                <h4 className="text-xl font-semibold mb-2">Join Our Community</h4>
                <p className="text-gray-300">
                  Connect with fellow learners and get exclusive updates on new courses and events.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4">
                <motion.a
                  href="#whatsapp"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="btn-secondary flex items-center justify-center space-x-2"
                >
                  <MessageCircle className="w-5 h-5" />
                  <span>Join WhatsApp Group</span>
                </motion.a>
                <motion.a
                  href="#review"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-white/10 hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <Star className="w-5 h-5" />
                  <span>Write a Review</span>
                </motion.a>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <motion.p
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                className="text-gray-400 text-sm"
              >
                Copyright © 2025 - All Rights Reserved | LAskill Training Center
              </motion.p>
              
              <motion.button
                onClick={scrollToTop}
                whileHover={{ scale: 1.1, y: -2 }}
                whileTap={{ scale: 0.95 }}
                className="w-10 h-10 bg-primary-600 hover:bg-primary-700 rounded-full flex items-center justify-center transition-all duration-300"
                aria-label="Scroll to top"
              >
                <ArrowUp className="w-5 h-5" />
              </motion.button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
