'use client'

import { motion } from 'framer-motion'
import { useInView } from 'framer-motion'
import { useRef } from 'react'
import { Calendar, Clock, MapPin, Users, ArrowRight, Star } from 'lucide-react'

const Events = () => {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const events = [
    {
      title: 'SkillUp 22.0 – 10-Week Digital Skill Internship Program',
      description: 'Learn a Skill That Pays! Kickstart your tech journey with our comprehensive digital skills internship program.',
      image: 'https://laskill.org/wp-content/uploads/2025/05/SkillUp-22-LAskill-300x300.jpeg',
      date: 'June 2024',
      duration: '10 Weeks',
      type: 'Internship',
      status: 'Open for Registration',
      featured: true,
      highlights: [
        'Hands-on training',
        'Real-world projects',
        'Mentorship included',
        'Certificate upon completion'
      ]
    },
    {
      title: 'Free Digital Skills Training in 5 Weeks – Apply for the CIBS Now!',
      description: 'What if 5 weeks could change the way you earn? Join our Charles <PERSON> Birthday Scholarship program.',
      image: 'https://laskill.org/wp-content/uploads/2025/05/<PERSON>-<PERSON><PERSON><PERSON><PERSON>-Birthday-Scholarship-LAskill-1-300x300.jpg',
      date: 'July 2024',
      duration: '5 Weeks',
      type: 'Scholarship',
      status: 'Limited Spots',
      featured: false,
      highlights: [
        'Completely free',
        'Birthday scholarship',
        'Fast-track learning',
        'Job placement support'
      ]
    },
    {
      title: 'SkillUp 21.0 – Legitimate Ways to Make Money Online',
      description: 'Unlock Legit Online Income Streams! Join us at SkillUp 21.0 and discover proven methods to earn online.',
      image: 'https://laskill.org/wp-content/uploads/2025/04/SkillUp-21-LAskill-Training-Center-300x300.jpg',
      date: 'August 2024',
      duration: '1 Day',
      type: 'Workshop',
      status: 'Coming Soon',
      featured: false,
      highlights: [
        'Online income strategies',
        'Practical demonstrations',
        'Q&A sessions',
        'Networking opportunities'
      ]
    },
    {
      title: 'SkillUp 20.0 – Winning The Influence Game in 2025 and Beyond',
      description: 'The journey of growth, learning, and mastery continues as we explore the future of digital influence.',
      image: 'https://laskill.org/wp-content/uploads/2025/04/SkillUp20-LAskill-Training-Center-300x300.jpg',
      date: 'September 2024',
      duration: '2 Days',
      type: 'Conference',
      status: 'Early Bird',
      featured: false,
      highlights: [
        'Future trends',
        'Influence strategies',
        'Expert speakers',
        'Interactive sessions'
      ]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="events" ref={ref} className="py-20 lg:py-32 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
            Upcoming{' '}
            <span className="text-gradient">Events</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Join our transformative events and accelerate your digital skills journey
          </p>
        </motion.div>

        {/* Featured Event */}
        {events.filter(event => event.featured).map((event, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, scale: 0.95 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-16"
          >
            <div className="card overflow-hidden relative">
              {/* Featured Badge */}
              <div className="absolute top-6 left-6 z-10">
                <div className="flex items-center space-x-2 bg-accent-500 text-white px-4 py-2 rounded-full">
                  <Star className="w-4 h-4" />
                  <span className="text-sm font-semibold">Featured Event</span>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                <div className="relative h-64 lg:h-auto">
                  <img
                    src={event.image}
                    alt={event.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  
                  {/* Status Badge */}
                  <div className="absolute bottom-6 left-6">
                    <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {event.status}
                    </span>
                  </div>
                </div>

                <div className="p-8 lg:p-12 flex flex-col justify-center">
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-sm font-medium">
                      {event.type}
                    </span>
                    <div className="flex items-center text-gray-500 text-sm">
                      <Clock className="w-4 h-4 mr-1" />
                      {event.duration}
                    </div>
                  </div>

                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 leading-tight">
                    {event.title}
                  </h3>

                  <p className="text-gray-600 mb-6 leading-relaxed">
                    {event.description}
                  </p>

                  {/* Event Details */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3">
                      <Calendar className="w-5 h-5 text-primary-500" />
                      <span className="text-gray-700">{event.date}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <MapPin className="w-5 h-5 text-primary-500" />
                      <span className="text-gray-700">Online & Physical</span>
                    </div>
                  </div>

                  {/* Highlights */}
                  <div className="grid grid-cols-2 gap-2 mb-6">
                    {event.highlights.map((highlight, idx) => (
                      <div key={idx} className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-accent-500 rounded-full" />
                        <span className="text-sm text-gray-600">{highlight}</span>
                      </div>
                    ))}
                  </div>

                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="btn-primary flex items-center space-x-2 w-fit"
                  >
                    <span>Apply Now</span>
                    <ArrowRight className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        ))}

        {/* Other Events Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {events.filter(event => !event.featured).map((event, index) => (
            <motion.div
              key={index}
              variants={cardVariants}
              whileHover={{ 
                scale: 1.02,
                transition: { duration: 0.2 }
              }}
              className="card overflow-hidden group cursor-pointer"
            >
              <div className="relative h-48 overflow-hidden">
                <img
                  src={event.image}
                  alt={event.title}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                
                {/* Status Badge */}
                <div className="absolute top-4 right-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    event.status === 'Open for Registration' ? 'bg-green-500 text-white' :
                    event.status === 'Limited Spots' ? 'bg-orange-500 text-white' :
                    event.status === 'Coming Soon' ? 'bg-blue-500 text-white' :
                    'bg-purple-500 text-white'
                  }`}>
                    {event.status}
                  </span>
                </div>
              </div>

              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-sm font-medium">
                    {event.type}
                  </span>
                  <div className="flex items-center text-gray-500 text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    {event.duration}
                  </div>
                </div>

                <h3 className="text-lg font-bold text-gray-900 mb-3 leading-tight group-hover:text-primary-600 transition-colors duration-200">
                  {event.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 leading-relaxed">
                  {event.description}
                </p>

                <div className="space-y-2 mb-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Calendar className="w-4 h-4" />
                    <span>{event.date}</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Users className="w-4 h-4" />
                    <span>Limited Seats Available</span>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="w-full bg-gray-100 hover:bg-primary-50 text-gray-800 hover:text-primary-600 font-semibold py-2 px-4 rounded-lg transition-all duration-300 flex items-center justify-center space-x-2"
                >
                  <span>Learn More</span>
                  <ArrowRight className="w-4 h-4" />
                </motion.button>
              </div>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

export default Events
